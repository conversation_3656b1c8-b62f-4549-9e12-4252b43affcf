<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOZR - Header Section</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }

        .header {
            background-color: #2d2d2d;
            padding: 12px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .left-section {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .logo {
            color: #d4af37;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 2px;
            text-decoration: none;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 400;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #d4af37;
        }

        .dropdown-arrow {
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 5px solid currentColor;
            transition: transform 0.3s ease;
        }

        .nav-item:hover .dropdown-arrow {
            transform: rotate(180deg);
        }

        /* Dropdown Menu Styles */
        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 280px;
            margin-top: 10px;
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-content {
            padding: 20px;
        }

        /* Equipment Rentals Multi-Column Dropdown */
        .equipment-dropdown {
            min-width: 1000px;
            left: -250px;
            max-height: 600px;
            overflow-y: auto;
        }

        .dropdown-columns {
            display: grid;
            grid-template-columns: repeat(5, minmax(180px, 1fr));
            gap: 25px;
            min-width: 950px;
            width: 100%;
        }

        .dropdown-column {
            min-width: 180px;
            max-width: 220px;
            overflow: hidden;
            position: relative;
        }

        .dropdown-column:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 0;
            right: -12px;
            width: 1px;
            height: 100%;
            background: linear-gradient(to bottom, 
                transparent 0%,
                #f0f0f0 20%,
                #e8e8e8 50%,
                #f0f0f0 80%,
                transparent 100%
            );
        }

        .dropdown-column h3 {
            color: #2d2d2d;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #d4af37;
            white-space: nowrap;
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
        }

        .dropdown-column ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .dropdown-column li {
            margin-bottom: 6px;
        }

        .dropdown-column a {
            color: #666;
            text-decoration: none;
            font-size: 13px;
            padding: 6px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
            max-width: 100%;
        }

        .dropdown-column a span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
            min-width: 0;
        }

        .dropdown-column a:hover {
            color: #d4af37;
            padding-left: 8px;
        }

        .dropdown-column a:hover span {
            white-space: normal;
            text-overflow: unset;
            overflow: visible;
            line-height: 1.3;
        }

        /* Equipment Icon Styles */
        .equipment-icon {
            width: 35px;
            height: 35px;
            background-color: #f5f5f5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: background-color 0.3s ease;
        }

        .dropdown-column a:hover .equipment-icon {
            background-color: #e8e8e8;
        }

        .equipment-svg {
            width: 20px;
            height: 20px;
        }

        /* Responsive adjustments for equipment dropdown */
        @media (max-width: 1200px) {
            .equipment-dropdown {
                min-width: 800px;
                left: -200px;
            }
            
            .dropdown-columns {
                grid-template-columns: repeat(4, 1fr);
                gap: 20px;
                min-width: 750px;
            }
        }

        @media (max-width: 1024px) {
            .equipment-dropdown {
                min-width: 600px;
                left: -150px;
            }
            
            .dropdown-columns {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
                min-width: 550px;
            }
        }

        @media (max-width: 768px) {
            .equipment-dropdown {
                min-width: 400px;
                left: -100px;
                position: fixed;
                top: 60px;
                right: 20px;
                left: 20px;
                min-width: auto;
                max-width: none;
            }
            
            .dropdown-columns {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
                min-width: auto;
            }
        }

        .phone-number {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .browse-btn {
            background-color: #2d2d2d;
            border: 2px solid #555;
            color: white;
            padding: 14px 28px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .browse-btn:hover {
            border-color: #d4af37;
            background-color: #3a3a3a;
        }

        .search-icon {
            width: 18px;
            height: 18px;
            fill: currentColor;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-container {
                padding: 0 15px;
            }
            
            .nav-left {
                gap: 20px;
                margin-left: 20px;
            }
            
            .phone-number {
                display: none;
            }
            
            .nav-link {
                font-size: 14px;
            }
        }

        @media (max-width: 640px) {
            .nav-left {
                display: none;
            }
            
            .logo {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <!-- Left Section with Logo and Navigation -->
            <div class="left-section">
                <!-- Logo -->
                <a href="#" class="logo">FORENTAS</a>
                
                <!-- Left Navigation -->
                <nav class="nav-left">
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            Equipment Rentals
                            <span class="dropdown-arrow"></span>
                        </a>
                        <!-- Equipment Rentals Dropdown Menu -->
                        <div class="dropdown-menu equipment-dropdown">
                            <div class="dropdown-content">
                                <div class="dropdown-columns">
                                    <div class="dropdown-column">
                                        <h3>Compact Equipment</h3>
                                        <ul>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="15" y="45" width="50" height="25" fill="#333" rx="3"/>
                                                            <rect x="10" y="50" width="15" height="15" fill="#555" rx="2"/>
                                                            <rect x="65" y="50" width="15" height="15" fill="#555" rx="2"/>
                                                            <rect x="25" y="35" width="30" height="15" fill="#d4af37" rx="2"/>
                                                            <circle cx="20" cy="75" r="8" fill="#333"/>
                                                            <circle cx="70" cy="75" r="8" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Compact Track Loaders</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="15" y="45" width="50" height="25" fill="#333" rx="3"/>
                                                            <rect x="10" y="50" width="15" height="15" fill="#555" rx="2"/>
                                                            <rect x="65" y="50" width="15" height="15" fill="#555" rx="2"/>
                                                            <circle cx="20" cy="75" r="8" fill="#333"/>
                                                            <circle cx="70" cy="75" r="8" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Wheeled Skid Steers</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="20" y="50" width="40" height="20" fill="#333" rx="3"/>
                                                            <rect x="45" y="30" width="20" height="25" fill="#d4af37" rx="2"/>
                                                            <path d="M65 40 L80 35 L85 45 L70 50 Z" fill="#555"/>
                                                            <circle cx="30" cy="75" r="6" fill="#333"/>
                                                            <circle cx="50" cy="75" r="6" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Mini Excavators</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="25" y="45" width="35" height="20" fill="#333" rx="3"/>
                                                            <rect x="35" y="35" width="15" height="15" fill="#d4af37" rx="2"/>
                                                            <path d="M60 50 L75 40 L80 55 L65 60 Z" fill="#555"/>
                                                            <circle cx="35" cy="70" r="7" fill="#333"/>
                                                            <circle cx="55" cy="70" r="7" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Backhoes</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="25" y="50" width="30" height="15" fill="#333" rx="2"/>
                                                            <rect x="30" y="40" width="20" height="12" fill="#d4af37" rx="2"/>
                                                            <circle cx="35" cy="70" r="6" fill="#333"/>
                                                            <circle cx="50" cy="70" r="6" fill="#333"/>
                                                            <rect x="15" y="55" width="15" height="8" fill="#555" rx="1"/>
                                                        </svg>
                                                    </div>
                                                    <span>Tractors</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div class="dropdown-column">
                                        <h3>Heavy Earthmoving</h3>
                                        <ul>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="30" y="45" width="40" height="25" fill="#d4af37" rx="3"/>
                                                            <rect x="45" y="25" width="15" height="25" fill="#333" rx="2"/>
                                                            <path d="M70 45 L85 35 L90 50 L75 55 Z" fill="#555"/>
                                                            <circle cx="40" cy="75" r="8" fill="#333"/>
                                                            <circle cx="60" cy="75" r="8" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Excavators</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="25" y="50" width="40" height="20" fill="#d4af37" rx="3"/>
                                                            <rect x="15" y="45" width="25" height="15" fill="#555" rx="2"/>
                                                            <circle cx="35" cy="75" r="8" fill="#333"/>
                                                            <circle cx="55" cy="75" r="8" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Wheel Loaders</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="20" y="50" width="50" height="20" fill="#d4af37" rx="3"/>
                                                            <rect x="15" y="45" width="20" height="10" fill="#555" rx="2"/>
                                                            <circle cx="30" cy="75" r="8" fill="#333"/>
                                                            <circle cx="60" cy="75" r="8" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Dozers</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="20" y="40" width="50" height="30" fill="#333" rx="5"/>
                                                            <rect x="25" y="35" width="40" height="10" fill="#d4af37" rx="2"/>
                                                            <circle cx="30" cy="75" r="8" fill="#333"/>
                                                            <circle cx="60" cy="75" r="8" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Rock Trucks</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div class="dropdown-column">
                                        <h3>Lifts and Aerial</h3>
                                        <ul>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="35" y="50" width="20" height="20" fill="#d4af37" rx="3"/>
                                                            <rect x="40" y="30" width="10" height="25" fill="#333" rx="1"/>
                                                            <rect x="37" y="25" width="16" height="8" fill="#555" rx="2"/>
                                                            <circle cx="40" cy="75" r="6" fill="#333"/>
                                                            <circle cx="50" cy="75" r="6" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Forklifts</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="30" y="60" width="30" height="15" fill="#333" rx="3"/>
                                                            <rect x="40" y="35" width="10" height="30" fill="#d4af37" rx="1"/>
                                                            <rect x="35" y="30" width="20" height="8" fill="#555" rx="2"/>
                                                            <rect x="25" y="65" width="8" height="8" fill="#d4af37" rx="1"/>
                                                            <rect x="57" y="65" width="8" height="8" fill="#d4af37" rx="1"/>
                                                        </svg>
                                                    </div>
                                                    <span>Scissor Lifts</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="25" y="65" width="25" height="15" fill="#333" rx="3"/>
                                                            <path d="M37 65 Q45 40 60 25" stroke="#d4af37" stroke-width="3" fill="none"/>
                                                            <rect x="55" y="20" width="15" height="8" fill="#555" rx="2"/>
                                                            <circle cx="30" cy="85" r="6" fill="#333"/>
                                                            <circle cx="45" cy="85" r="6" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Articulating Boom Lifts</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="25" y="65" width="25" height="15" fill="#333" rx="3"/>
                                                            <rect x="35" y="25" width="5" height="45" fill="#d4af37" rx="1"/>
                                                            <rect x="30" y="20" width="15" height="8" fill="#555" rx="2"/>
                                                            <circle cx="30" cy="85" r="6" fill="#333"/>
                                                            <circle cx="45" cy="85" r="6" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Straight Boom Lifts</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="20" y="70" width="30" height="10" fill="#333" rx="2"/>
                                                            <path d="M35 70 L45 35" stroke="#d4af37" stroke-width="3"/>
                                                            <rect x="40" y="30" width="12" height="6" fill="#555" rx="1"/>
                                                            <circle cx="25" cy="85" r="5" fill="#333"/>
                                                            <circle cx="45" cy="85" r="5" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Towable Boom Lifts</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="30" y="50" width="25" height="20" fill="#333" rx="3"/>
                                                            <rect x="20" y="40" width="15" height="8" fill="#d4af37" rx="1"/>
                                                            <rect x="55" y="30" width="8" height="25" fill="#555" rx="1"/>
                                                            <circle cx="35" cy="75" r="6" fill="#333"/>
                                                            <circle cx="50" cy="75" r="6" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Telehandler Reach Forklift</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div class="dropdown-column">
                                        <h3>Compaction</h3>
                                        <ul>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="25" y="50" width="40" height="15" fill="#d4af37" rx="3"/>
                                                            <rect x="35" y="40" width="20" height="12" fill="#333" rx="2"/>
                                                            <ellipse cx="30" cy="70" rx="12" ry="6" fill="#555"/>
                                                            <ellipse cx="60" cy="70" rx="12" ry="6" fill="#555"/>
                                                        </svg>
                                                    </div>
                                                    <span>Soil Compaction Smooth Drum</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="25" y="50" width="40" height="15" fill="#d4af37" rx="3"/>
                                                            <rect x="35" y="40" width="20" height="12" fill="#333" rx="2"/>
                                                            <ellipse cx="30" cy="70" rx="12" ry="6" fill="#555"/>
                                                            <circle cx="60" cy="70" r="8" fill="#555"/>
                                                            <circle cx="56" cy="66" r="1" fill="#333"/>
                                                            <circle cx="64" cy="66" r="1" fill="#333"/>
                                                            <circle cx="56" cy="74" r="1" fill="#333"/>
                                                            <circle cx="64" cy="74" r="1" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Soil Compaction Pad Foot</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="25" y="50" width="40" height="15" fill="#d4af37" rx="3"/>
                                                            <rect x="35" y="40" width="20" height="12" fill="#333" rx="2"/>
                                                            <ellipse cx="30" cy="70" rx="12" ry="6" fill="#555"/>
                                                            <ellipse cx="60" cy="70" rx="12" ry="6" fill="#555"/>
                                                        </svg>
                                                    </div>
                                                    <span>Asphalt Compaction Double Drum</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div class="dropdown-column">
                                        <h3>Trucks</h3>
                                        <ul>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="15" y="45" width="55" height="25" fill="#333" rx="3"/>
                                                            <rect x="20" y="35" width="20" height="15" fill="#d4af37" rx="2"/>
                                                            <rect x="45" y="40" width="20" height="10" fill="#555" rx="1"/>
                                                            <circle cx="25" cy="75" r="7" fill="#333"/>
                                                            <circle cx="60" cy="75" r="7" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Trucks</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="equipment-icon">
                                                        <svg viewBox="0 0 100 100" class="equipment-svg">
                                                            <rect x="15" y="35" width="55" height="35" fill="#333" rx="3"/>
                                                            <rect x="20" y="30" width="20" height="15" fill="#d4af37" rx="2"/>
                                                            <rect x="45" y="40" width="20" height="20" fill="#555" rx="1"/>
                                                            <circle cx="25" cy="75" r="7" fill="#333"/>
                                                            <circle cx="60" cy="75" r="7" fill="#333"/>
                                                        </svg>
                                                    </div>
                                                    <span>Cube Trucks</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <a href="tel:**************" class="phone-number">**************</a>
                </nav>
            </div>
            
            <!-- Right Navigation -->
            <div class="nav-right">
                <a href="#" class="browse-btn">
                    <svg class="search-icon" viewBox="0 0 24 24">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                    Browse Rentals
                </a>
            </div>
        </div>
    </header>

    <!-- Demo content to show sticky header -->
    <div style="height: 200vh; background: linear-gradient(to bottom, #f0f0f0, #e0e0e0); padding: 40px 20px;">
        <div style="max-width: 1200px; margin: 0 auto;">
            <h1 style="color: #2d2d2d; margin-bottom: 20px;">DOZR Header Demo</h1>
            <p style="color: #666; line-height: 1.6;">
                This is the header section for the DOZR website rebuild. The header includes:
            </p>
            <ul style="color: #666; margin: 20px 0; padding-left: 20px;">
                <li>DOZR logo in brand gold color</li>
                <li>Navigation menu with dropdown arrows</li>
                <li>Phone number for customer support</li>
                <li>Browse Rentals button with search icon</li>
                <li>Shopping cart with item count</li>
                <li>Responsive design that adapts to mobile screens</li>
                <li>Sticky positioning that stays at top when scrolling</li>
            </ul>
            <p style="color: #666;">
                Scroll down to see the sticky header behavior in action!
            </p>
        </div>
    </div>
</body>
</html>